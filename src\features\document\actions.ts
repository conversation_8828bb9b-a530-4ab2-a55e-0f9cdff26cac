'use server';

import { revalidatePath } from 'next/cache';
import { createServerAction } from 'zsa';
import { z } from 'zod';

import prisma from '@/lib/prisma';

import {
  createDocumentSchema,
  updateDocumentSchema,
  deleteDocumentSchema,
  documentFilterSchema,
  documentStatsSchema,
  getDocumentsSchema,
  getDocumentByIdSchema,
  createDocumentOutputSchema,
  updateDocumentOutputSchema,
  deleteDocumentOutputSchema,
  generateDocumentFromTemplateSchema,
  updateDocumentContentSchema,
  documentTemplateSchema,
  generateAIDocumentSchema,
  googleDriveTemplateSchema,
} from './schemas';
import { generateDocumentWithAI as generateAIContent } from '@/lib/gemini';

export const getDocuments = createServerAction()
  .input(documentFilterSchema.optional())
  .output(getDocumentsSchema)
  .handler(async ({ input: filters }) => {
    return prisma.document.findMany({
      where: {
        ...(filters?.caseId && { caseId: filters.caseId }),
        ...(filters?.type && { type: filters.type }),
        ...(filters?.status && { status: filters.status }),
        ...(filters?.search && {
          OR: [
            { name: { contains: filters.search, mode: 'insensitive' } },
            {
              case: {
                caseNumber: { contains: filters.search, mode: 'insensitive' },
              },
            },
            {
              case: {
                debtorName: { contains: filters.search, mode: 'insensitive' },
              },
            },
          ],
        }),
      },
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
          },
        },
      },
      orderBy: {
        uploadDate: 'desc',
      },
    });
  });

export const getDocumentById = createServerAction()
  .input(
    z.object({ id: z.string().min(1, 'El ID del documento es requerido') }),
  )
  .output(getDocumentByIdSchema)
  .handler(async ({ input: { id } }) => {
    const document = await prisma.document.findUnique({
      where: { id },
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
          },
        },
      },
    });

    if (!document) {
      throw new Error('Documento no encontrado');
    }

    return document;
  });

export const createDocument = createServerAction()
  .input(createDocumentSchema)
  .output(createDocumentOutputSchema)
  .handler(async ({ input: data }) => {
    const document = await prisma.document.create({
      data: {
        ...data,
        status: data.status ?? 'PENDIENTE',
      },
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
          },
        },
      },
    });

    revalidatePath('/documents');
    return document;
  });

export const updateDocument = createServerAction()
  .input(updateDocumentSchema)
  .output(updateDocumentOutputSchema)
  .handler(async ({ input: data }) => {
    const { id: documentId, ...updateData } = data;
    const document = await prisma.document.update({
      where: { id: documentId },
      data: updateData,
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
          },
        },
      },
    });

    revalidatePath('/documents');
    return document;
  });

export const deleteDocument = createServerAction()
  .input(deleteDocumentSchema)
  .output(deleteDocumentOutputSchema)
  .handler(async ({ input: { id } }) => {
    const deletedDocument = await prisma.document.delete({
      where: { id },
    });

    revalidatePath('/documents');
    return deletedDocument;
  });

// Acciones para plantillas de documentos
export const getDocumentTemplates = createServerAction()
  .input(z.object({}).optional())
  .output(z.array(documentTemplateSchema))
  .handler(async () => {
    const templates = await prisma.documentTemplate.findMany({
      orderBy: {
        fileName: 'asc',
      },
    });

    return templates.map((template) => ({
      ...template,
      placeholders: Array.isArray(template.placeholders)
        ? (template.placeholders as Array<{
            type: 'text' | 'number' | 'date' | 'boolean';
            required: boolean;
            key: string;
            label: string;
            description?: string;
            defaultValue?: string;
          }>)
        : [],
    }));
  });

export const updateDocumentTemplate = createServerAction()
  .input(
    z.object({
      id: z.string().min(1, 'El ID de la plantilla es requerido'),
      placeholders: z
        .array(
          z.object({
            key: z.string().min(1, 'La clave es requerida'),
            label: z.string().min(1, 'La etiqueta es requerida'),
            type: z.enum(['text', 'number', 'date', 'email']),
            required: z.boolean(),
            description: z.string().optional(),
          }),
        )
        .optional(),
    }),
  )
  .output(documentTemplateSchema)
  .handler(async ({ input: data }) => {
    const { id, ...updateData } = data;

    const template = await prisma.documentTemplate.update({
      where: { id },
      data: updateData,
    });

    revalidatePath('/documents/templates');
    return {
      ...template,
      placeholders: Array.isArray(template.placeholders)
        ? (template.placeholders as Array<{
            type: 'text' | 'number' | 'date' | 'boolean';
            required: boolean;
            key: string;
            label: string;
            description?: string;
            defaultValue?: string;
          }>)
        : [],
    };
  });

export const downloadDocumentTemplate = createServerAction()
  .input(
    z.object({ id: z.string().min(1, 'El ID de la plantilla es requerido') }),
  )
  .output(
    z.object({
      buffer: z.any(),
      fileName: z.string(),
      mimeType: z.string(),
    }),
  )
  .handler(async ({ input: { id } }) => {
    const { googleDriveService } = await import('@/lib/google-drive');

    const template = await prisma.documentTemplate.findUnique({
      where: { id },
    });

    if (!template) {
      throw new Error('Plantilla no encontrada');
    }

    const buffer = await googleDriveService.downloadFile(
      template.googleDriveId,
    );

    return {
      buffer,
      fileName: template.fileName,
      mimeType: template.mimeType,
    };
  });

export const deleteDocumentTemplate = createServerAction()
  .input(
    z.object({ id: z.string().min(1, 'El ID de la plantilla es requerido') }),
  )
  .output(z.object({ success: z.boolean() }))
  .handler(async ({ input: { id } }) => {
    const { googleDriveService } = await import('@/lib/google-drive');

    // Obtener la plantilla para conseguir el ID de Google Drive
    const template = await prisma.documentTemplate.findUnique({
      where: { id },
    });

    if (!template) {
      throw new Error('Plantilla no encontrada');
    }

    // Eliminar archivo de Google Drive
    try {
      await googleDriveService.deleteFile(template.googleDriveId);
    } catch (error) {
      console.error('Error eliminando archivo de Google Drive:', error);
      // Continuar con la eliminación de la base de datos aunque falle Google Drive
    }

    // Eliminar de la base de datos
    await prisma.documentTemplate.delete({
      where: { id },
    });

    revalidatePath('/documents/templates');
    return { success: true };
  });

export const getGoogleDriveFoldersAndFiles = createServerAction()
  .input(z.object({ folderId: z.string().optional() }).optional())
  .output(
    z.object({
      folders: z.array(
        z.object({
          id: z.string(),
          name: z.string(),
          createdTime: z.string().optional(),
          modifiedTime: z.string().optional(),
        }),
      ),
      files: z.array(
        z.object({
          id: z.string(),
          name: z.string(),
          mimeType: z.string().optional(),
          size: z.string().optional(),
          createdTime: z.string().optional(),
          modifiedTime: z.string().optional(),
          parents: z.array(z.string()).optional(),
        }),
      ),
    }),
  )
  .handler(async ({ input }) => {
    const { googleDriveService } = await import('@/lib/google-drive');

    const result = await googleDriveService.listFoldersAndFiles(
      input?.folderId,
    );

    return {
      folders: result.folders.map((folder) => ({
        id: folder.id!,
        name: folder.name!,
        createdTime: folder.createdTime || undefined,
        modifiedTime: folder.modifiedTime || undefined,
      })),
      files: result.files.map((file) => ({
        id: file.id!,
        name: file.name!,
        mimeType: file.mimeType || undefined,
        size: file.size || undefined,
        createdTime: file.createdTime || undefined,
        modifiedTime: file.modifiedTime || undefined,
        parents: file.parents || undefined,
      })),
    };
  });

export const generateDocumentFromTemplate = createServerAction()
  .input(generateDocumentFromTemplateSchema)
  .output(createDocumentOutputSchema)
  .handler(async ({ input: data }) => {
    // Obtener la plantilla
    const template = await prisma.documentTemplate.findUnique({
      where: { id: data.templateId },
    });

    if (!template) {
      throw new Error('Plantilla no encontrada');
    }

    // Obtener datos del caso para el contexto
    const caseData = await prisma.case.findUnique({
      where: { id: data.caseId },
      include: {
        debtor: true,
        operator: true,
      },
    });

    if (!caseData) {
      throw new Error('Caso no encontrado');
    }

    // Aquí se implementaría la lógica de generación del documento
    // usando la biblioteca docx-templates para reemplazar placeholders

    const document = await prisma.document.create({
      data: {
        name: data.name,
        type: 'DOCUMENTO_GENERADO',
        status: 'GENERADO',
        url: '', // Se generaría la URL después de guardar el archivo
        caseId: data.caseId,
        templateId: data.templateId,
        isGenerated: true,
      },
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
          },
        },
      },
    });

    revalidatePath('/documents');
    return document;
  });

export const updateDocumentContent = createServerAction()
  .input(updateDocumentContentSchema)
  .output(z.object({ success: z.boolean() }))
  .handler(async ({ input: data }) => {
    // Obtener el documento actual
    const currentDocument = await prisma.document.findUnique({
      where: { id: data.documentId },
    });

    if (!currentDocument) {
      throw new Error('Documento no encontrado');
    }

    // For now, we'll just update the document name or status
    // In the future, this could upload a new version to Google Drive
    await prisma.document.update({
      where: { id: data.documentId },
      data: {
        name: data.content.substring(0, 100), // Use first 100 chars as name
        status: 'ACTUALIZADO',
      },
    });

    revalidatePath('/documents');
    return { success: true };
  });

export const getDocumentStats = createServerAction()
  .output(documentStatsSchema)
  .handler(async () => {
    const [total, byStatus, byType] = await Promise.all([
      prisma.document.count(),
      prisma.document.groupBy({
        by: ['status'],
        _count: {
          id: true,
        },
      }),
      prisma.document.groupBy({
        by: ['type'],
        _count: {
          id: true,
        },
      }),
    ]);

    return {
      total,
      byStatus,
      byType,
    };
  });

// Acción para generar documentos con IA usando plantillas de Google Drive
export const generateDocumentWithAI = createServerAction()
  .input(generateAIDocumentSchema)
  .output(createDocumentOutputSchema)
  .handler(async ({ input: data }) => {
    const { googleDriveService } = await import('@/lib/google-drive');

    // Obtener los datos del caso
    const caseData = await prisma.case.findUnique({
      where: { id: data.caseId },
      include: {
        debtor: {
          select: {
            name: true,
            email: true,
            phone: true,
            address: true,
            idNumber: true,
            city: true,
            department: true,
            monthlyIncome: true,
            monthlyExpenses: true,
          },
        },
        debts: {
          include: {
            creditor: {
              select: {
                name: true,
              },
            },
          },
        },
        operator: {
          select: {
            name: true,
            email: true,
            phone: true,
            professionalCard: true,
          },
        },
      },
    });

    if (!caseData) {
      throw new Error('Caso no encontrado');
    }

    // Obtener información del template de Google Drive
    const templateInfo = await googleDriveService.getFileInfo(data.templateId);
    if (!templateInfo) {
      throw new Error('Plantilla no encontrada en Google Drive');
    }

    // Descargar el contenido de la plantilla
    const templateBuffer = await googleDriveService.downloadFile(
      data.templateId,
    );

    // Procesar la plantilla con IA para reemplazar placeholders
    await generateAIContent({
      documentType: templateInfo.name || 'Documento Legal',
      caseData: {
        id: caseData.id,
        caseNumber: caseData.caseNumber,
        debtorName: caseData.debtorName,
        type: caseData.type,
        status: caseData.status,
        createdAt: caseData.createdDate,
        updatedAt: caseData.createdDate,
        debtorEmail: caseData.debtor?.email,
        debtorPhone: caseData.debtor?.phone,
        debtorAddress: caseData.debtor?.address,
        totalDebt: Number(caseData.totalDebt),
        creditors:
          caseData.debts?.map((debt) => ({
            name: debt.creditor.name,
            amount: Number(debt.amount),
            type: debt.type,
          })) || [],
        hearings: [],
      },
      instructions: data.instructions,
      templateBuffer: templateBuffer,
    });

    // Crear el documento en la base de datos
    const document = await prisma.document.create({
      data: {
        name: data.name,
        type: templateInfo.name || 'Documento Generado',
        status: 'APROBADO',
        url: `documents/${Date.now()}-${data.name.replace(/\s+/g, '-').toLowerCase()}.docx`,
        uploadDate: new Date(),
        caseId: data.caseId,
        isGenerated: true,
      },
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
            createdDate: true,
          },
        },
      },
    });

    revalidatePath('/documents');
    revalidatePath(`/cases/${data.caseId}`);

    return document;
  });

// Acción para obtener plantillas de Google Drive
export const getGoogleDriveTemplates = createServerAction()
  .input(z.object({}).optional())
  .output(z.array(googleDriveTemplateSchema))
  .handler(async () => {
    const { googleDriveService } = await import('@/lib/google-drive');

    // Get files from the main templates folder
    const result = await googleDriveService.listFoldersAndFiles();

    // Filter only Word documents (.docx files)
    const templates = result.files.filter(
      (file) =>
        file.mimeType ===
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
        file.name?.toLowerCase().endsWith('.docx'),
    );

    return templates.map((template) => ({
      id: template.id!,
      name: template.name!,
      mimeType: template.mimeType || undefined,
      size: template.size || undefined,
      createdTime: template.createdTime || undefined,
      modifiedTime: template.modifiedTime || undefined,
    }));
  });
