'use client';

import { useRef, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useServerAction } from 'zsa-react';
import { toast } from 'sonner';
import { Loader2, Wand2 } from 'lucide-react';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  generateDocumentWithAI,
  getGoogleDriveTemplates,
} from '@/features/document/actions';
import type { GoogleDriveTemplate } from '@/features/document/schemas';

// Schema for AI document generation
const generateAIDocumentSchema = z.object({
  name: z.string().min(1, 'El nombre del documento es requerido'),
  caseId: z.string().min(1, 'Debe seleccionar un caso'),
  templateId: z.string().min(1, 'Debe seleccionar una plantilla'),
  instructions: z.string().optional(),
  templateFile: z.instanceof(File).optional(),
});

type GenerateAIDocumentData = z.infer<typeof generateAIDocumentSchema>;

interface GenerateDocumentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  cases: Array<{
    id: string;
    caseNumber: string;
    debtorName: string;
    type: string;
  }>;
  onDocumentGenerated: (document: unknown) => void;
}

export function GenerateDocumentDialog({
  open,
  onOpenChange,
  cases,
  onDocumentGenerated,
}: Readonly<GenerateDocumentDialogProps>) {
  const closeRef = useRef<HTMLButtonElement>(null);
  const [templates, setTemplates] = useState<GoogleDriveTemplate[]>([]);
  const [loadingTemplates, setLoadingTemplates] = useState(false);

  // Real AI document generation action
  const { execute: executeGenerate, isPending } = useServerAction(
    generateDocumentWithAI,
    {
      onSuccess: ({ data }) => {
        toast.success('Documento generado exitosamente con IA');
        onDocumentGenerated(data);
        closeRef.current?.click();
      },
      onError: ({ err }) => {
        toast.error(err.message || 'Error al generar el documento con IA');
      },
    },
  );

  // Load templates action
  const { execute: loadTemplates } = useServerAction(getGoogleDriveTemplates, {
    onSuccess: ({ data }) => {
      setTemplates(data);
      setLoadingTemplates(false);
    },
    onError: ({ err }) => {
      toast.error(err.message || 'Error al cargar las plantillas');
      setLoadingTemplates(false);
    },
  });

  const form = useForm<GenerateAIDocumentData>({
    resolver: zodResolver(generateAIDocumentSchema),
    defaultValues: {
      name: '',
      caseId: '',
      templateId: '',
      instructions: '',
    },
  });

  // Load templates when dialog opens
  useEffect(() => {
    if (open && templates.length === 0) {
      setLoadingTemplates(true);
      loadTemplates({});
    }
  }, [open, templates.length, loadTemplates]);

  const caseId = form.watch('caseId');

  // Auto-generate document name when case or template is selected
  useEffect(() => {
    if (caseId) {
      const selectedCase = cases.find((c) => c.id === caseId);
      const templateId = form.getValues('templateId');
      const selectedTemplate = templates.find((t) => t.id === templateId);
      if (selectedCase && selectedTemplate) {
        const templateName = selectedTemplate.name.replace('.docx', '');
        const autoName = `${templateName} - ${selectedCase.caseNumber}`;
        form.setValue('name', autoName);
      }
    }
  }, [caseId, cases, form, templates]);

  const onSubmit = (data: GenerateAIDocumentData) => {
    executeGenerate(data);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Wand2 className="h-5 w-5 text-blue-600" />
            <span>Generar Documento con IA</span>
          </DialogTitle>
          <DialogDescription>
            Utilice inteligencia artificial para generar documentos legales
            personalizados con datos del caso
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="templateId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Plantilla de Google Drive</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={loadingTemplates}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue
                            placeholder={
                              loadingTemplates
                                ? 'Cargando plantillas...'
                                : 'Seleccione una plantilla'
                            }
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="max-w-[400px]">
                        {templates.map((template) => (
                          <SelectItem
                            key={template.id}
                            value={template.id}
                            className="truncate"
                          >
                            {template.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="caseId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Caso</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Seleccione un caso" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {cases.map((case_) => (
                          <SelectItem key={case_.id} value={case_.id}>
                            {case_.caseNumber} - {case_.debtorName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nombre del Documento</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Nombre del documento generado"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Card className="border-blue-200 bg-blue-50">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-blue-800">
                  <Wand2 className="h-5 w-5" />
                  <span>Generación Inteligente</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-blue-700">
                    La IA utilizará automáticamente los datos del caso
                    seleccionado para generar un documento personalizado y
                    completo.
                  </p>
                  <div className="grid grid-cols-2 gap-4 text-xs text-blue-600">
                    <div>• Datos del deudor</div>
                    <div>• Información del caso</div>
                    <div>• Fechas relevantes</div>
                    <div>• Montos y cálculos</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-end space-x-3">
              <DialogClose asChild>
                <Button
                  type="button"
                  variant="outline"
                  ref={closeRef}
                  disabled={isPending}
                >
                  Cancelar
                </Button>
              </DialogClose>
              <Button
                type="submit"
                disabled={
                  !form.watch('caseId') ||
                  !form.watch('templateId') ||
                  isPending
                }
                className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700"
              >
                {isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Generando con IA...</span>
                  </>
                ) : (
                  <>
                    <Wand2 className="h-4 w-4" />
                    <span>Generar con IA</span>
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
