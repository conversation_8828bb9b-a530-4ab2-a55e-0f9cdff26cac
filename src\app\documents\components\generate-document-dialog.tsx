'use client';

import { useRef, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from 'sonner';
import { Loader2, Wand2 } from 'lucide-react';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Schema for AI document generation
const generateAIDocumentSchema = z.object({
  name: z.string().min(1, 'El nombre del documento es requerido'),
  caseId: z.string().min(1, 'Debe seleccionar un caso'),
  documentType: z.string().min(1, 'Debe seleccionar un tipo de documento'),
  instructions: z.string().optional(),
  templateFile: z.instanceof(File).optional(),
});

type GenerateAIDocumentData = z.infer<typeof generateAIDocumentSchema>;

interface GenerateDocumentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  cases: Array<{
    id: string;
    caseNumber: string;
    debtorName: string;
    type: string;
  }>;
  onDocumentGenerated: (document: unknown) => void;
}

export function GenerateDocumentDialog({
  open,
  onOpenChange,
  cases,
  onDocumentGenerated,
}: Readonly<GenerateDocumentDialogProps>) {
  const closeRef = useRef<HTMLButtonElement>(null);
  // Mock isPending state for the loading UI
  const isPending = false;

  const form = useForm<GenerateAIDocumentData>({
    resolver: zodResolver(generateAIDocumentSchema),
    defaultValues: {
      name: '',
      caseId: '',
      documentType: '',
      instructions: '',
    },
  });

  const caseId = form.watch('caseId');

  // Auto-generate document name when case is selected
  useEffect(() => {
    if (caseId) {
      const selectedCase = cases.find((c) => c.id === caseId);
      const documentType = form.getValues('documentType');
      if (selectedCase && documentType) {
        const autoName = `${documentType} - ${selectedCase.caseNumber}`;
        form.setValue('name', autoName);
      }
    }
  }, [caseId, cases, form]);

  const onSubmit = (data: GenerateAIDocumentData) => {
    // Mock implementation - will be replaced with actual AI generation
    const mockDocument = {
      id: String(Date.now()),
      name: data.name,
      type: data.documentType,
      caseId: data.caseId,
      debtorName: cases.find((c) => c.id === data.caseId)?.debtorName || 'N/A',
      status: 'Generado',
      createdDate: new Date().toISOString().split('T')[0],
      size: '2 MB',
      format: 'DOCX',
      createdBy: 'IA Generativa',
      downloadCount: 0,
      lastAccessed: new Date().toISOString(),
    };

    setTimeout(() => {
      toast.success('Documento generado exitosamente con IA');
      onDocumentGenerated(mockDocument);
      closeRef.current?.click();
    }, 2000);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Wand2 className="h-5 w-5 text-blue-600" />
            <span>Generar Documento con IA</span>
          </DialogTitle>
          <DialogDescription>
            Utilice inteligencia artificial para generar documentos legales
            personalizados con datos del caso
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="documentType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tipo de Documento</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Seleccione el tipo" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Auto de Admisión">
                          Auto de Admisión
                        </SelectItem>
                        <SelectItem value="Notificación a Acreedores">
                          Notificación a Acreedores
                        </SelectItem>
                        <SelectItem value="Suspensión de Procesos">
                          Suspensión de Procesos
                        </SelectItem>
                        <SelectItem value="Acuerdo de Pago">
                          Acuerdo de Pago
                        </SelectItem>
                        <SelectItem value="Tabla de Amortización">
                          Tabla de Amortización
                        </SelectItem>
                        <SelectItem value="Certificado REDAM">
                          Certificado REDAM
                        </SelectItem>
                        <SelectItem value="Extractos Bancarios">
                          Extractos Bancarios
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="caseId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Caso</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Seleccione un caso" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {cases.map((case_) => (
                          <SelectItem key={case_.id} value={case_.id}>
                            {case_.caseNumber} - {case_.debtorName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nombre del Documento</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Nombre del documento generado"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Card className="border-blue-200 bg-blue-50">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-blue-800">
                  <Wand2 className="h-5 w-5" />
                  <span>Generación Inteligente</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-blue-700">
                    La IA utilizará automáticamente los datos del caso
                    seleccionado para generar un documento personalizado y
                    completo.
                  </p>
                  <div className="grid grid-cols-2 gap-4 text-xs text-blue-600">
                    <div>• Datos del deudor</div>
                    <div>• Información del caso</div>
                    <div>• Fechas relevantes</div>
                    <div>• Montos y cálculos</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-end space-x-3">
              <DialogClose asChild>
                <Button
                  type="button"
                  variant="outline"
                  ref={closeRef}
                  disabled={isPending}
                >
                  Cancelar
                </Button>
              </DialogClose>
              <Button
                type="submit"
                disabled={
                  !form.watch('caseId') ||
                  !form.watch('documentType') ||
                  isPending
                }
                className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700"
              >
                {isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Generando con IA...</span>
                  </>
                ) : (
                  <>
                    <Wand2 className="h-4 w-4" />
                    <span>Generar con IA</span>
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
