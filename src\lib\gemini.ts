import { GoogleGenerativeAI } from '@google/generative-ai';

if (!process.env.GOOGLE_GEMINI_API_KEY) {
  throw new Error('GOOGLE_GEMINI_API_KEY is not set in environment variables');
}

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GEMINI_API_KEY);

export const geminiModel = genAI.getGenerativeModel({
  model: 'gemini-2.5-flash',
});

export interface CaseData {
  id: string;
  caseNumber: string;
  debtorName: string;
  type: string;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  debtorEmail?: string;
  debtorPhone?: string;
  debtorAddress?: string;
  totalDebt?: number;
  creditors?: Array<{
    name: string;
    amount: number;
    type: string;
  }>;
  hearings?: Array<{
    date: Date;
    type: string;
    status: string;
  }>;
}

export interface DocumentGenerationRequest {
  documentType: string;
  caseData: CaseData;
  instructions?: string;
  templateBuffer?: Buffer;
}

export async function generateDocumentWithAI(
  request: DocumentGenerationRequest,
): Promise<string> {
  const { documentType, caseData, instructions, templateBuffer } = request;

  // Si hay una plantilla, procesarla con IA para reemplazar placeholders
  if (templateBuffer) {
    // Convertir el buffer a texto (simplificado para demostración)
    let templateText = '';
    try {
      templateText = templateBuffer.toString('utf-8');
    } catch (error) {
      console.error('Error convirtiendo buffer a texto:', error);
      templateText = 'Error al procesar la plantilla';
    }

    const templatePrompt = `
Eres un asistente legal especializado en derecho de insolvencia en Colombia. Tu tarea es completar una plantilla de documento legal reemplazando los marcadores (placeholders) con datos reales del caso.

INFORMACIÓN DEL CASO:
- Número de Caso: ${caseData.caseNumber}
- Nombre del Deudor: ${caseData.debtorName}
- Tipo de Caso: ${caseData.type}
- Estado: ${caseData.status}
- Email: ${caseData.debtorEmail || 'No disponible'}
- Teléfono: ${caseData.debtorPhone || 'No disponible'}
- Dirección: ${caseData.debtorAddress || 'No disponible'}
- Deuda Total: ${caseData.totalDebt ? `$${caseData.totalDebt.toLocaleString('es-CO')}` : 'No disponible'}
- Fecha de Creación: ${caseData.createdAt.toLocaleDateString('es-CO')}

${
  caseData.creditors && caseData.creditors.length > 0
    ? `
ACREEDORES:
${caseData.creditors.map((c) => `- ${c.name}: $${c.amount.toLocaleString('es-CO')} (${c.type})`).join('\n')}
`
    : ''
}

PLANTILLA A COMPLETAR:
"""
${templateText}
"""

INSTRUCCIONES:
1. Identifica todos los marcadores (placeholders) en la plantilla. Los marcadores suelen estar en formato XXXX o [XXXX] o {{XXXX}}.
2. Reemplaza cada marcador con la información correspondiente del caso.
3. Si no encuentras información específica para un marcador, usa un valor razonable basado en el contexto.
4. Mantén el formato y estructura del documento original.
5. Asegúrate de que el documento final sea coherente y profesional.
6. Usa lenguaje jurídico apropiado y formal.

${instructions ? `INSTRUCCIONES ADICIONALES: ${instructions}` : ''}

FORMATO DE RESPUESTA:
Devuelve únicamente el contenido del documento completo con los marcadores reemplazados, sin explicaciones adicionales.
`;

    try {
      const result = await geminiModel.generateContent(templatePrompt);
      const response = result.response;
      const text = response.text();

      if (!text || text.trim().length === 0) {
        throw new Error('La IA no pudo procesar la plantilla');
      }

      return text.trim();
    } catch (error) {
      console.error('Error procesando plantilla con IA:', error);
      throw new Error(
        'Error al procesar la plantilla con IA: ' +
          (error instanceof Error ? error.message : 'Error desconocido'),
      );
    }
  }

  // Si no hay plantilla, generar documento desde cero
  const prompt = `
Eres un asistente legal especializado en derecho de insolvencia en Colombia. Tu tarea es generar un documento legal profesional y completo.

INFORMACIÓN DEL CASO:
- Número de Caso: ${caseData.caseNumber}
- Nombre del Deudor: ${caseData.debtorName}
- Tipo de Caso: ${caseData.type}
- Estado: ${caseData.status}
- Email: ${caseData.debtorEmail || 'No disponible'}
- Teléfono: ${caseData.debtorPhone || 'No disponible'}
- Dirección: ${caseData.debtorAddress || 'No disponible'}
- Deuda Total: ${caseData.totalDebt ? `$${caseData.totalDebt.toLocaleString('es-CO')}` : 'No disponible'}
- Fecha de Creación: ${caseData.createdAt.toLocaleDateString('es-CO')}

${
  caseData.creditors && caseData.creditors.length > 0
    ? `
ACREEDORES:
${caseData.creditors.map((c) => `- ${c.name}: $${c.amount.toLocaleString('es-CO')} (${c.type})`).join('\n')}
`
    : ''
}

${
  caseData.hearings && caseData.hearings.length > 0
    ? `
AUDIENCIAS:
${caseData.hearings.map((h) => `- ${h.date.toLocaleDateString('es-CO')}: ${h.type} - ${h.status}`).join('\n')}
`
    : ''
}

TIPO DE DOCUMENTO A GENERAR: ${documentType}

${instructions ? `INSTRUCCIONES ADICIONALES: ${instructions}` : ''}

INSTRUCCIONES:
1. Genera un documento legal profesional y completo en español colombiano
2. Usa el formato apropiado para el tipo de documento solicitado
3. Incluye todos los datos relevantes del caso
4. Usa lenguaje jurídico apropiado y formal
5. Incluye fechas, números de caso y referencias legales cuando sea apropiado
6. El documento debe estar listo para ser usado en procedimientos legales
7. Incluye encabezados, numeración y estructura apropiada
8. Usa las leyes y normativas colombianas aplicables

FORMATO DE RESPUESTA:
Devuelve únicamente el contenido del documento en formato de texto plano, sin explicaciones adicionales.
`;

  try {
    const result = await geminiModel.generateContent(prompt);
    const response = result.response;
    const text = response.text();

    if (!text || text.trim().length === 0) {
      throw new Error('La IA no pudo generar contenido para el documento');
    }

    return text.trim();
  } catch (error) {
    console.error('Error generating document with AI:', error);
    throw new Error(
      'Error al generar el documento con IA: ' +
        (error instanceof Error ? error.message : 'Error desconocido'),
    );
  }
}

export function getDocumentTypePromptEnhancement(documentType: string): string {
  const enhancements: Record<string, string> = {
    'Auto de Admisión': `
      - Incluye la admisión del proceso de insolvencia
      - Menciona los requisitos legales cumplidos
      - Establece los plazos procesales
      - Incluye la designación del administrador si aplica
    `,
    'Notificación a Acreedores': `
      - Lista completa de acreedores conocidos
      - Montos adeudados a cada acreedor
      - Plazos para presentar créditos
      - Procedimiento para verificación de créditos
    `,
    'Suspensión de Procesos': `
      - Identifica los procesos ejecutivos a suspender
      - Establece el alcance de la suspensión
      - Menciona las excepciones legales
      - Incluye la duración de la suspensión
    `,
    'Acuerdo de Pago': `
      - Términos específicos del acuerdo
      - Cronograma de pagos detallado
      - Garantías ofrecidas si las hay
      - Consecuencias del incumplimiento
    `,
    'Tabla de Amortización': `
      - Cálculo detallado de cuotas
      - Distribución de capital e intereses
      - Fechas específicas de pago
      - Saldos pendientes por período
    `,
    'Certificado REDAM': `
      - Información completa del registro
      - Estado actual del deudor
      - Historial de procesos de insolvencia
      - Vigencia del certificado
    `,
    'Extractos Bancarios': `
      - Análisis de movimientos financieros
      - Identificación de ingresos y egresos
      - Capacidad de pago demostrada
      - Período de análisis especificado
    `,
  };

  return enhancements[documentType] || '';
}
